import Foundation

/// Класс для управления настройками звуков
class SoundSettings {
    
    // MARK: - Singleton
    
    static let shared = SoundSettings()
    
    // MARK: - UserDefaults Keys
    
    private let isSessionSoundEnabledKey = "isSessionSoundEnabled"
    private let selectedSessionSoundKey = "selectedSessionSound"
    private let isBreakSoundEnabledKey = "isBreakSoundEnabled"
    private let selectedBreakSoundKey = "selectedBreakSound"
    
    // MARK: - Properties
    
    private let userDefaults = UserDefaults.standard
    
    /// Включен ли звук для завершения сессий
    var isSessionSoundEnabled: Bool {
        get {
            // По умолчанию включено
            if userDefaults.object(forKey: isSessionSoundEnabledKey) == nil {
                return true
            }
            return userDefaults.bool(forKey: isSessionSoundEnabledKey)
        }
        set {
            userDefaults.set(newValue, forKey: isSessionSoundEnabledKey)
            print("🔊 SoundSettings: Звук сессий \(newValue ? "включен" : "выключен")")
        }
    }
    
    /// Выбранный звук для завершения сессий
    var selectedSessionSound: String {
        get {
            return userDefaults.string(forKey: selectedSessionSoundKey) ?? SoundManager.getDefaultSessionSound()
        }
        set {
            userDefaults.set(newValue, forKey: selectedSessionSoundKey)
            print("🔊 SoundSettings: Выбран звук сессий: \(SoundManager.getDisplayName(for: newValue))")
        }
    }
    
    /// Включен ли звук для завершения отдыхов
    var isBreakSoundEnabled: Bool {
        get {
            // По умолчанию включено
            if userDefaults.object(forKey: isBreakSoundEnabledKey) == nil {
                return true
            }
            return userDefaults.bool(forKey: isBreakSoundEnabledKey)
        }
        set {
            userDefaults.set(newValue, forKey: isBreakSoundEnabledKey)
            print("🔊 SoundSettings: Звук отдыхов \(newValue ? "включен" : "выключен")")
        }
    }
    
    /// Выбранный звук для завершения отдыхов
    var selectedBreakSound: String {
        get {
            return userDefaults.string(forKey: selectedBreakSoundKey) ?? SoundManager.getDefaultBreakSound()
        }
        set {
            userDefaults.set(newValue, forKey: selectedBreakSoundKey)
            print("🔊 SoundSettings: Выбран звук отдыхов: \(SoundManager.getDisplayName(for: newValue))")
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        // Приватный инициализатор для singleton
    }
    
    // MARK: - Public Methods
    
    /// Сбрасывает все настройки звуков к значениям по умолчанию
    func resetToDefaults() {
        isSessionSoundEnabled = true
        selectedSessionSound = SoundManager.getDefaultSessionSound()
        isBreakSoundEnabled = true
        selectedBreakSound = SoundManager.getDefaultBreakSound()
        
        print("🔊 SoundSettings: Настройки звуков сброшены к значениям по умолчанию")
    }
    
    /// Проверяет и исправляет настройки звуков (если файлы не существуют)
    func validateAndFixSettings() {
        // Проверяем звук сессий
        if !SoundManager.soundExists(fileName: selectedSessionSound) {
            print("⚠️ SoundSettings: Звук сессий не найден: \(selectedSessionSound), сбрасываем к умолчанию")
            selectedSessionSound = SoundManager.getDefaultSessionSound()
        }
        
        // Проверяем звук отдыхов
        if !SoundManager.soundExists(fileName: selectedBreakSound) {
            print("⚠️ SoundSettings: Звук отдыхов не найден: \(selectedBreakSound), сбрасываем к умолчанию")
            selectedBreakSound = SoundManager.getDefaultBreakSound()
        }
    }
    
    /// Возвращает текущие настройки в виде строки для отладки
    func debugDescription() -> String {
        return """
        SoundSettings:
        - Session sound enabled: \(isSessionSoundEnabled)
        - Selected session sound: \(selectedSessionSound) (\(SoundManager.getDisplayName(for: selectedSessionSound)))
        - Break sound enabled: \(isBreakSoundEnabled)
        - Selected break sound: \(selectedBreakSound) (\(SoundManager.getDisplayName(for: selectedBreakSound)))
        """
    }
}
