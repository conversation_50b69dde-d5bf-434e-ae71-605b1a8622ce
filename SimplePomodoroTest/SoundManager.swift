import Foundation
import AVFoundation
import Cocoa

/// Менеджер для управления воспроизведением звуков
class SoundManager {
    
    // MARK: - Properties
    
    private var audioPlayer: AVAudioPlayer?
    private let soundSettings = SoundSettings.shared
    
    // MARK: - Sound Types
    
    enum SoundType {
        case sessionCompleted
        case breakCompleted
    }
    
    // MARK: - Available Sounds
    
    struct SoundFile {
        let fileName: String
        let displayName: String
        let filePath: String
        
        init(fileName: String, displayName: String) {
            self.fileName = fileName
            self.displayName = displayName
            self.filePath = "Sounds/\(fileName)"
        }
    }
    
    // Список всех доступных звуков
    static let availableSounds: [SoundFile] = [
        SoundFile(fileName: "session_done/session_1.mp3", displayName: "Мелодия 1"),
        SoundFile(fileName: "break_done/break_1.mp3", displayName: "Мелодия 2"),
        SoundFile(fileName: "1.mp3", displayName: "Мелодия 3"),
        SoundFile(fileName: "2.mp3", displayName: "Мелодия 4"),
        SoundFile(fileName: "3.mp3", displayName: "Мелодия 5")
    ]
    
    // MARK: - Initialization
    
    init() {
        setupAudioSession()
    }
    
    // MARK: - Public Methods
    
    /// Воспроизводит звук для указанного типа события
    func playSound(for type: SoundType) {
        switch type {
        case .sessionCompleted:
            NSLog("🔊 SoundManager: Событие завершения сессии. Включен: \(soundSettings.isSessionSoundEnabled), файл: \(soundSettings.selectedSessionSound)")
            if soundSettings.isSessionSoundEnabled {
                NSLog("🔊 SoundManager: Воспроизводим звук завершения сессии")
                playSound(fileName: soundSettings.selectedSessionSound)
            } else {
                NSLog("🔇 SoundManager: Звук завершения сессии отключен")
            }
        case .breakCompleted:
            NSLog("🔊 SoundManager: Событие завершения отдыха. Включен: \(soundSettings.isBreakSoundEnabled), файл: \(soundSettings.selectedBreakSound)")
            if soundSettings.isBreakSoundEnabled {
                NSLog("🔊 SoundManager: Воспроизводим звук завершения отдыха")
                playSound(fileName: soundSettings.selectedBreakSound)
            } else {
                NSLog("🔇 SoundManager: Звук завершения отдыха отключен")
            }
        }
    }
    
    /// Воспроизводит конкретный звуковой файл (для тестирования)
    func playSound(fileName: String) {
        guard let soundFile = Self.availableSounds.first(where: { $0.fileName == fileName }) else {
            print("❌ SoundManager: Звуковой файл не найден: \(fileName)")
            return
        }

        // Пытаемся найти файл в разных местах
        var url: URL?

        // Сначала пробуем найти в Bundle
        if soundFile.fileName.contains("/") {
            let components = soundFile.fileName.split(separator: "/")
            if components.count == 2 {
                let subdirectory = "Sounds/\(components[0])"
                let filename = String(components[1]).replacingOccurrences(of: ".mp3", with: "")
                url = Bundle.main.url(forResource: filename, withExtension: "mp3", subdirectory: subdirectory)
            }
        } else {
            let filename = soundFile.fileName.replacingOccurrences(of: ".mp3", with: "")
            url = Bundle.main.url(forResource: filename, withExtension: "mp3", subdirectory: "Sounds")
        }

        // Если не нашли в Bundle, пробуем найти в папке Sounds рядом с приложением
        if url == nil {
            let bundlePath = Bundle.main.bundlePath
            let soundsPath = URL(fileURLWithPath: bundlePath).deletingLastPathComponent().appendingPathComponent("Sounds").appendingPathComponent(soundFile.fileName)
            if FileManager.default.fileExists(atPath: soundsPath.path) {
                url = soundsPath
            }
        }

        guard let finalUrl = url else {
            print("❌ SoundManager: Не удалось найти файл: \(soundFile.fileName)")
            return
        }

        do {
            audioPlayer = try AVAudioPlayer(contentsOf: finalUrl)
            audioPlayer?.volume = 0.7 // Умеренная громкость
            audioPlayer?.play()
            print("🔊 SoundManager: Воспроизводится звук: \(soundFile.displayName)")
        } catch {
            print("❌ SoundManager: Ошибка воспроизведения звука: \(error.localizedDescription)")
        }
    }
    
    /// Останавливает воспроизведение звука
    func stopSound() {
        audioPlayer?.stop()
        audioPlayer = nil
    }
    
    // MARK: - Private Methods
    
    private func setupAudioSession() {
        // На macOS AVAudioSession недоступен, поэтому просто логируем
        print("🔊 SoundManager: Аудио сессия готова (macOS)")
    }
    
    // MARK: - Static Helper Methods
    
    /// Возвращает звук по умолчанию для сессий
    static func getDefaultSessionSound() -> String {
        return "session_done/session_1.mp3"
    }
    
    /// Возвращает звук по умолчанию для отдыхов
    static func getDefaultBreakSound() -> String {
        return "break_done/break_1.mp3"
    }
    
    /// Возвращает отображаемое имя для звукового файла
    static func getDisplayName(for fileName: String) -> String {
        return availableSounds.first(where: { $0.fileName == fileName })?.displayName ?? fileName
    }
    
    /// Проверяет, существует ли звуковой файл
    static func soundExists(fileName: String) -> Bool {
        return availableSounds.contains(where: { $0.fileName == fileName })
    }
}
